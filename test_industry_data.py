# -*- coding: utf-8 -*-
"""
测试行业数据获取功能
"""

import sys
import os
import asyncio
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'member-api'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_industry_data():
    """测试行业数据获取"""
    try:
        from core.models import QueryParams
        from api.PPTreport.IndustryData import industry_data_service
        
        # 创建测试查询参数
        query_params = QueryParams(
            query_type="member_base",
            bid="test_bid",
            sid="test_sid", 
            start_date="2025-06-01",
            end_date="2025-06-30",
            cashier_system="pinzhi",
            merchant_id=None
        )
        
        print("🔍 开始测试行业数据获取...")
        print(f"查询参数: {query_params}")
        
        # 获取行业分析数据
        industry_data = await industry_data_service.get_industry_analysis_data(query_params)
        
        print(f"✅ 行业数据获取完成!")
        print(f"数据量: {len(industry_data) if industry_data else 0}")
        
        if industry_data:
            print("\n📊 数据字段:")
            for key, value in industry_data.items():
                print(f"  {key}: {value}")
        else:
            print("❌ 未获取到行业数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_industry_data())
