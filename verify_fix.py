#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复的简单脚本
"""

def verify_industry_data_fix():
    """验证IndustryData.py的修复"""
    print("🔍 验证IndustryData.py修复...")
    
    # 读取文件内容
    with open('member-api/api/PPTreport/IndustryData.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查修复项目
    checks = [
        ("导入member_charge_service", "from api.query.MemberChargeTab import member_charge_service"),
        ("初始化member_charge_service", "self.member_charge_service = member_charge_service"),
        ("获取四个模块数据", "member_base_data, member_consume_data, member_charge_data, coupon_data"),
        ("方法签名包含member_charge_data", "def _extract_industry_indicators(self, member_base_data, member_consume_data, member_charge_data, coupon_data)"),
        ("移除错误的coupon_trade_amount", "result['coupon_trade_amount']" not in content),
        ("添加储值留存率提取", "result['prepay_retention_rate'] = self._extract_field_value(member_charge_data, 'retention_rate')"),
        ("更新空数据方法", "'prepay_retention_rate': 0.0")
    ]
    
    for check_name, check_content in checks:
        if isinstance(check_content, bool):
            if check_content:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
        else:
            if check_content in content:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")

def verify_constants_fix():
    """验证constants.py的修复"""
    print("\n🔍 验证constants.py修复...")
    
    # 读取文件内容
    with open('member-api/api/PPTreport/constants.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查修复项目
    checks = [
        ("移除coupon_trade_amount参数", 'params["coupon_trade_amount"]' not in content),
        ("添加prepay_retention_rate参数", 'params["prepay_retention_rate"]'),
        ("添加prepay_retention_rate_year参数", 'params["prepay_retention_rate_year"]')
    ]
    
    for check_name, check_content in checks:
        if isinstance(check_content, bool):
            if check_content:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
        else:
            if check_content in content:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")

def main():
    """主函数"""
    print("🚀 开始验证修复...")
    verify_industry_data_fix()
    verify_constants_fix()
    print("\n✅ 验证完成!")
    
    print("\n📋 修复总结:")
    print("1. 修复了IndustryData.py中错误的coupon_trade_amount字段引用")
    print("2. 添加了member_charge_service获取储值留存率数据")
    print("3. 更新了PPT参数生成逻辑，使用正确的储值实收沉淀率")
    print("4. PPT参数名称: prepay_retention_rate (当前时间段), prepay_retention_rate_year (年度)")

if __name__ == "__main__":
    main()
