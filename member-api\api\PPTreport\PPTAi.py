# -*- coding: utf-8 -*-
"""
PPT AI分析模块
专门处理member_data_analysis_report和member_revenue_analysis_report参数的AI分析
"""

import logging
from typing import Dict, Any, List
import asyncio

from services.llm_service import LLMService
from .promt import AIAnalysisPrompts

logger = logging.getLogger(__name__)

class PPTAiAnalyzer:
    """PPT AI分析器 - 使用promt.py中的AI分析功能"""

    def __init__(self):
        """初始化PPT AI分析器"""
        self.llm_service = LLMService()
        logger.info("PPT AI分析器初始化完成")

    def _create_member_data_analysis_prompt(self, ppt_data: Dict[str, Any]) -> str:
        """
        创建会员数据分析的提示词，结合promt.py中的模板选择

        Args:
            ppt_data: PPT页面的所有数据

        Returns:
            str: 格式化的提示词
        """
        # 计算关键比例 - 安全转换字符串为数字
        def safe_int(value, default=0):
            """安全转换为整数"""
            if isinstance(value, str):
                try:
                    return int(value.replace(',', ''))
                except (ValueError, AttributeError):
                    return default
            return int(value) if value else default

        total_members = safe_int(ppt_data.get('total_members', 0))
        new_members = safe_int(ppt_data.get('new_members', 0))
        complete_phone_members = safe_int(ppt_data.get('complete_phone_members', 0))
        total_complete_members = safe_int(ppt_data.get('total_complete_members', 0))
        total_consume_members = safe_int(ppt_data.get('total_consume_members', 0))
        total_charge_members = safe_int(ppt_data.get('total_charge_members', 0))

        new_member_ratio = (new_members / total_members * 100) if total_members > 0 else 0
        phone_complete_ratio = (complete_phone_members / total_members * 100) if total_members > 0 else 0
        info_complete_ratio = (total_complete_members / total_members * 100) if total_members > 0 else 0
        consume_ratio = (total_consume_members / total_members * 100) if total_members > 0 else 0
        charge_ratio = (total_charge_members / total_members * 100) if total_members > 0 else 0

        # 获取时间范围信息
        time_frame = ppt_data.get('query_params', {}).get('time_frame', '数据统计期间')

        # 获取promt.py中的模板句式供AI选择
        member_templates = AIAnalysisPrompts.MEMBER_DATA_ANALYSIS_TEMPLATES

        prompt = f"""
你是一位专业的会员运营数据分析师，请基于以下会员数据进行深度分析，从提供的模板句式中选择最合适的3个进行分析。

## 数据统计时间范围
**分析期间：{time_frame}**

## 会员数据详情
**基础数据：**
- 会员总量：{total_members:,}人
- 新增会员：{new_members:,}人（占比{new_member_ratio:.1f}%）
- 历史会员：{ppt_data.get('historical_members', 'N/A')}人

**信息完善度：**
- 完善手机号：{complete_phone_members:,}人（{phone_complete_ratio:.1f}%）
- 未完善手机号：{ppt_data.get('complete_nophone_members', 'N/A')}人
- 完善基础资料：{total_complete_members:,}人（{info_complete_ratio:.1f}%）
- 未完善资料：{ppt_data.get('total_nocomplete_members', 'N/A')}人

**活跃度表现：**
- 有消费行为：{total_consume_members:,}人（{consume_ratio:.1f}%）
- 无消费行为：{ppt_data.get('total_noconsume_members', 'N/A')}人
- 有充值行为：{total_charge_members:,}人（{charge_ratio:.1f}%）
- 无充值行为：{ppt_data.get('total_nocharge_members', 'N/A')}人

## 可选择的分析模板句式
{chr(10).join([f"{i+1}. {template}" for i, template in enumerate(member_templates)])}

## 分析任务
基于上述会员数据，识别关键问题并提供具体的改进建议。重点关注问题诊断和解决方案，避免简单重复数据。

## 输出要求
请严格按照以下格式输出3条分析，每条分析独占一行：

1、[问题识别 + 具体建议]
2、[问题识别 + 具体建议]
3、[问题识别 + 具体建议]

分析重点：
- 优先识别数据中反映的关键问题（如增长瓶颈、转化率低、活跃度不足等）
- 提供具体可执行的改进建议，避免泛泛而谈
- 减少数据重复描述，重点突出洞察和解决方案
- 每条分析控制在80字以内，确保内容充实且简洁
- 必须基于{time_frame}期间的实际数据进行分析
"""
        return prompt

    async def analyze_member_data_report(self, ppt_data: Dict[str, Any]) -> str:
        """
        分析会员数据并生成member_data_analysis_report参数
        结合大模型分析和promt.py模板选择

        Args:
            ppt_data: PPT页面的所有数据

        Returns:
            str: 格式化的分析结果（1、2、3格式）
        """
        try:
            logger.info("开始分析member_data_analysis_report...")

            # 创建结合模板的提示词
            prompt = self._create_member_data_analysis_prompt(ppt_data)

            # 调用大模型进行分析
            analysis_result = await self.llm_service.generate_response(prompt)

            # 验证和格式化结果
            formatted_result = self._validate_and_format_analysis(analysis_result)

            logger.info(f"member_data_analysis_report分析完成: {formatted_result}")
            return formatted_result

        except Exception as e:
            logger.error(f"member_data_analysis_report分析失败: {str(e)}")
            return self._get_fallback_member_analysis()

    def _create_member_revenue_analysis_prompt(self, ppt_data: Dict[str, Any]) -> str:
        """
        创建会员收入分析的提示词，结合promt.py中的模板选择

        Args:
            ppt_data: PPT页面的所有数据

        Returns:
            str: 格式化的提示词
        """
        # 处理金额数据（移除逗号）
        total_actual_amount_str = str(ppt_data.get('total_actual_amount', '0')).replace(',', '')
        prepay_actual_amount_str = str(ppt_data.get('prepay_actual_amount', '0')).replace(',', '')
        first_consume_amount_str = str(ppt_data.get('first_consume_amount', '0')).replace(',', '')
        repeat_consume_amount_str = str(ppt_data.get('repeat_consume_amount', '0')).replace(',', '')

        try:
            total_actual_amount = float(total_actual_amount_str)
            prepay_actual_amount = float(prepay_actual_amount_str)
            # first_consume_amount 和 repeat_consume_amount 在prompt中使用
            _ = float(first_consume_amount_str)  # 验证数据有效性
            _ = float(repeat_consume_amount_str)  # 验证数据有效性

            # 计算关键比例
            prepay_ratio = (prepay_actual_amount / total_actual_amount * 100) if total_actual_amount > 0 else 0

            # 从ppt_data中获取repeat_consume_amount用于计算比例
            repeat_amount_str = str(ppt_data.get('repeat_consume_amount', '0')).replace(',', '')
            repeat_amount = float(repeat_amount_str)
            repeat_ratio = (repeat_amount / total_actual_amount * 100) if total_actual_amount > 0 else 0

            consume_users_str = str(ppt_data.get('consume_users', '0')).replace(',', '')
            consume_users = int(consume_users_str)
            per_capita = total_actual_amount / consume_users if consume_users > 0 else 0

        except (ValueError, TypeError) as e:
            logger.warning(f"数据转换失败: {e}")
            total_actual_amount = 0
            prepay_actual_amount = 0
            prepay_ratio = 0
            repeat_ratio = 0
            per_capita = 0
            consume_users = 0

        # 获取时间范围信息
        time_frame = ppt_data.get('query_params', {}).get('time_frame', '数据统计期间')

        # 获取promt.py中的收入分析模板句式供AI选择
        revenue_templates = AIAnalysisPrompts.MEMBER_REVENUE_ANALYSIS_TEMPLATES

        prompt = f"""
你是一位专业的财务数据分析师，请基于以下会员收入数据进行深度分析，从提供的模板句式中选择最合适的3个进行分析。

## 数据统计时间范围
**分析期间：{time_frame}**

## 收入数据详情
**收入规模：**
- 总实际消费：{ppt_data.get('total_actual_amount', 'N/A')}元
- 储值消费：{ppt_data.get('prepay_actual_amount', 'N/A')}元（占比{prepay_ratio:.1f}%）
- 现金消费：{total_actual_amount - prepay_actual_amount:,.0f}元（占比{100-prepay_ratio:.1f}%）

**消费行为：**
- 消费频次：{ppt_data.get('consume_frequency', 'N/A')}次
- 平均消费：{ppt_data.get('avg_consume_amount', 'N/A')}元
- 消费用户：{ppt_data.get('consume_users', 'N/A')}人
- 人均消费：{per_capita:,.0f}元

**用户价值：**
- 首次消费：{ppt_data.get('first_consume_amount', 'N/A')}元
- 重复消费：{ppt_data.get('repeat_consume_amount', 'N/A')}元（占比{repeat_ratio:.1f}%）

## 可选择的分析模板句式
{chr(10).join([f"{i+1}. {template}" for i, template in enumerate(revenue_templates)])}

## 分析任务
基于上述收入数据，识别收入结构和增长中的关键问题，提供具体的优化策略。重点关注问题诊断和解决方案。

## 输出要求
请严格按照以下格式输出3条分析，每条分析独占一行：

1、[收入问题识别 + 具体优化建议]
2、[收入问题识别 + 具体优化建议]
3、[收入问题识别 + 具体优化建议]

分析重点：
- 优先识别收入数据中的关键问题（如结构失衡、增长乏力、转化率低等）
- 提供具体可执行的收入优化策略，避免泛泛而谈
- 减少数据重复描述，重点突出商业洞察和解决方案
- 每条分析控制在80字以内，确保策略具体且可操作
- 必须基于{time_frame}期间的实际收入数据进行分析
"""
        return prompt

    async def analyze_member_revenue_report(self, ppt_data: Dict[str, Any]) -> str:
        """
        分析会员收入并生成member_revenue_analysis_report参数
        结合大模型分析和promt.py模板选择

        Args:
            ppt_data: PPT页面的所有数据

        Returns:
            str: 格式化的分析结果（1、2、3格式）
        """
        try:
            logger.info("开始分析member_revenue_analysis_report...")

            # 创建结合模板的提示词
            prompt = self._create_member_revenue_analysis_prompt(ppt_data)

            # 调用大模型进行分析
            analysis_result = await self.llm_service.generate_response(prompt)

            # 验证和格式化结果
            formatted_result = self._validate_and_format_analysis(analysis_result)

            logger.info(f"member_revenue_analysis_report分析完成: {formatted_result}")
            return formatted_result

        except Exception as e:
            logger.error(f"member_revenue_analysis_report分析失败: {str(e)}")
            return self._get_fallback_revenue_analysis()

    def _validate_and_format_analysis(self, analysis_result: str) -> str:
        """
        验证和格式化AI分析结果

        Args:
            analysis_result: AI原始分析结果

        Returns:
            str: 格式化后的分析结果
        """
        if not analysis_result or "AI分析暂时不可用" in analysis_result:
            return "数据分析暂时无法生成，请检查数据完整性。"

        # 按行分割
        lines = analysis_result.strip().split('\n')
        formatted_lines = []

        for line in lines:
            line = line.strip()
            # 匹配1、2、3开头的分析条目
            if line and (line.startswith('1、') or line.startswith('2、') or line.startswith('3、')):
                formatted_lines.append(line)

        # 如果没有找到正确格式的行，尝试从原文本中提取
        if len(formatted_lines) < 3:
            logger.warning("AI分析结果格式不标准，尝试重新格式化")
            # 尝试按句号分割并重新格式化
            sentences = [s.strip() for s in analysis_result.split('。') if s.strip() and len(s.strip()) > 10]
            formatted_lines = []

            for i, sentence in enumerate(sentences[:3], 1):
                if sentence and not sentence.startswith(str(i) + '、'):
                    formatted_lines.append(f"{i}、{sentence}。")
                elif sentence:
                    formatted_lines.append(sentence if sentence.endswith('。') else sentence + '。')

        # 确保有3条分析，如果不足则使用默认内容
        while len(formatted_lines) < 3:
            index = len(formatted_lines) + 1
            formatted_lines.append(f"{index}、数据分析中，建议关注关键指标变化趋势。")

        # 记录分析结果的质量
        logger.info(f"AI分析结果格式化完成，共{len(formatted_lines)}条分析")

        return '\n'.join(formatted_lines[:3])

    def _get_fallback_member_analysis(self) -> str:
        """获取会员数据分析的备用结果"""
        return """1、新会员获取成本偏高且留存率不足，建议优化获客渠道并建立新手引导体系。
2、会员信息完善率低影响精准营销，建议设置完善奖励并简化信息收集流程。
3、会员活跃度分化严重存在流失风险，建议建立分层运营和预警机制。"""

    def _get_fallback_revenue_analysis(self) -> str:
        """获取收入分析的备用结果"""
        return """1、储值消费占比过高存在资金风险，建议推动现金消费并优化储值策略。
2、客单价和消费频次双低制约收入增长，建议推出套餐产品和复购激励。
3、高价值用户贡献不足且流失率高，建议建立VIP服务体系和挽留机制。"""

    def process_coupon_data_for_ppt(self, coupon_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        处理优惠券数据，确保PPT中有8个券位

        Args:
            coupon_data: 优惠券数据列表

        Returns:
            Dict: 处理后的PPT参数字典
        """
        try:
            logger.info(f"开始处理优惠券数据，原始数据条数: {len(coupon_data)}")

            # 按带动现金消费金额排序（降序）- 与DataAcquisition.py保持一致
            # 适配CouponTradeTab.py的数据格式
            sorted_coupons = sorted(
                coupon_data,
                key=lambda x: float(x.get('driveCashAmount', x.get('drive_cash_amount', 0))),
                reverse=True
            )

            # 准备PPT参数
            ppt_params = {}

            # 处理8个券位
            for i in range(1, 9):  # 1到8
                if i <= len(sorted_coupons):
                    # 有数据的券
                    coupon = sorted_coupons[i-1]

                    # 适配CouponTradeTab.py的数据格式
                    coupon_id = coupon.get("couponId", coupon.get("coupon_id", f"CPN00{i}"))
                    coupon_name = coupon.get("couponName", coupon.get("coupon_name", f"优惠券{i}"))
                    send_count = int(coupon.get("couponSendCount", coupon.get("send_count", 0)))
                    used_count = int(coupon.get("couponUsedCount", coupon.get("used_count", 0)))
                    usage_rate = float(coupon.get("couponUsageRate", 0))
                    drive_amount = float(coupon.get("driveTotalAmount", coupon.get("drive_amount", 0)))

                    ppt_params[f"coupon_id{i}"] = coupon_id
                    ppt_params[f"coupon_name{i}"] = coupon_name
                    ppt_params[f"coupon_send_count{i}"] = str(send_count)
                    ppt_params[f"coupon_used_count{i}"] = str(used_count)

                    # 如果没有预计算的使用率，则计算
                    if usage_rate == 0 and send_count > 0:
                        usage_rate = (used_count / send_count * 100)

                    ppt_params[f"coupon_usage_rate{i}"] = f"{usage_rate:.1f}%"
                    ppt_params[f"drive_total_amount{i}"] = f"{drive_amount:,.2f}"

                    logger.info(f"券位{i}: {coupon_name} - 发券{send_count}张")
                else:
                    # 空券位
                    ppt_params[f"coupon_id{i}"] = ""
                    ppt_params[f"coupon_name{i}"] = ""
                    ppt_params[f"coupon_send_count{i}"] = ""
                    ppt_params[f"coupon_used_count{i}"] = ""
                    ppt_params[f"coupon_usage_rate{i}"] = ""
                    ppt_params[f"drive_total_amount{i}"] = ""

                    logger.info(f"券位{i}: 空券位")

            # 计算汇总数据 - 适配CouponTradeTab.py的数据格式
            total_send_count = sum(
                int(coupon.get("couponSendCount", coupon.get("send_count", 0)))
                for coupon in coupon_data
            )
            total_used_count = sum(
                int(coupon.get("couponUsedCount", coupon.get("used_count", 0)))
                for coupon in coupon_data
            )
            total_drive_amount = sum(
                float(coupon.get("driveTotalAmount", coupon.get("drive_amount", 0)))
                for coupon in coupon_data
            )
            avg_usage_rate = (total_used_count / total_send_count * 100) if total_send_count > 0 else 0

            ppt_params["total_send_count"] = str(total_send_count)
            ppt_params["total_used_count"] = str(total_used_count)
            ppt_params["avg_usage_rate"] = f"{avg_usage_rate:.1f}%"
            ppt_params["total_drive_amount"] = f"{total_drive_amount:,.2f}"

            logger.info(f"优惠券数据处理完成 - 总发券: {total_send_count}, 总使用: {total_used_count}")
            return ppt_params

        except Exception as e:
            logger.error(f"处理优惠券数据失败: {str(e)}")
            # 返回空券位数据
            empty_params = {}
            for i in range(1, 9):
                empty_params[f"coupon_id{i}"] = ""
                empty_params[f"coupon_name{i}"] = ""
                empty_params[f"coupon_send_count{i}"] = ""
                empty_params[f"coupon_used_count{i}"] = ""
                empty_params[f"coupon_usage_rate{i}"] = ""
                empty_params[f"drive_total_amount{i}"] = ""

            empty_params.update({
                "total_send_count": "0",
                "total_used_count": "0",
                "avg_usage_rate": "0%",
                "total_drive_amount": "0.00"
            })

            return empty_params


# 创建全局PPT AI分析器实例
ppt_ai_analyzer = PPTAiAnalyzer()